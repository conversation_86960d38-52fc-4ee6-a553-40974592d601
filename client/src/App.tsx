/**
 * Main App Component for SahAI CEP Extension V2
 * Root component that renders the TopBar and manages global state
 */

import React, { useEffect } from 'react';
import TopBar from './features/TopBar/TopBar';
import ChatMessages from './features/ChatMessages/ChatMessages';
import InputArea from './features/InputArea/InputArea';
import { UnifiedModal } from './components/ui/Modal';
import { useSettingsStore } from './stores/settingsStore';
import { useChatStore } from './stores/chatStore';
import { useModalStore } from './stores/modalStore';
import { useTheme } from './hooks/useTheme';
import './App.css';

const App: React.FC = () => {
  const {
    initializeDefaultProviders,
    initializeEnhancedApi,
    refreshAllProviders
  } = useSettingsStore();
  const { initializeTheme } = useTheme();
  const { createNewSession } = useChatStore();
  const { isOpen: isModalOpen, closeModal } = useModalStore();



  // Initialize the application
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout;

    const initializeApp = async () => {
      try {
        console.log('🚀 SahAI V2 App - Initializing...');

        // Initialize Adobe theme detection first
        await initializeTheme();

        // Initialize default providers
        initializeDefaultProviders();

        // Initialize enhanced API service (launches proxy)
        console.log('🔄 Initializing enhanced API service...');
        const apiInitialized = await initializeEnhancedApi();

        if (apiInitialized) {
          console.log('✅ Enhanced API service initialized successfully');

          // Initial model refresh for configured providers
          await refreshAllProviders();

          // Set up background refresh every 5 minutes
          refreshInterval = setInterval(() => {
            // Only refresh when document is visible to avoid unnecessary API calls
            if (!document.hidden) {
              console.log('🔄 Background model refresh triggered');
              refreshAllProviders();
            }
          }, 5 * 60 * 1000); // 5 minutes

          console.log('✅ Background refresh scheduled');
        } else {
          console.warn('⚠️ Enhanced API service failed to initialize - using fallback mode');
        }

        console.log('✅ SahAI V2 App - Ready');
      } catch (error) {
        console.error('❌ SahAI V2: Failed to initialize application:', error);
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        console.log('🔄 Background refresh cleanup');
      }
    };
  }, [initializeTheme, initializeDefaultProviders, initializeEnhancedApi, refreshAllProviders]);

  const handleNewChat = () => {
    console.log('📝 New chat requested');
    createNewSession();
  };

  return (
    <div className="sahai-app">
      <div className="app-container">
        {/* TopBar with Status Indicator → Clickable Link → New Chat → History → Settings */}
        <TopBar onNewChat={handleNewChat} />

        {/* Main content area - Chat interface */}
        <div className="main-content">
          <ChatMessages />
        </div>

        {/* InputArea - Enhanced with file attachment, voice input, and context references */}
        <InputArea
          onAttachFile={() => {
            console.log('📎 File attachment initiated');
            // File attachment is now implemented in InputArea component
          }}
          onVoiceInput={() => {
            console.log('🎤 Voice input initiated');
            // Voice input is now implemented in InputArea component
          }}
          onContextReference={() => {
            console.log('@ Context reference initiated');
            // Context reference is now implemented in InputArea component
          }}
        />
      </div>

      {/* UnifiedModal - Consolidated modal system supporting both centered and slide-in variants */}
      <UnifiedModal
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
};

export default App;

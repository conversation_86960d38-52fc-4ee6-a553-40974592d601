/**
 * ModelLoadingStatus Component
 * Enhanced error handling and user feedback for model loading states
 */

import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';

interface ModelLoadingStatusProps {
  className?: string;
  showProxyStatus?: boolean;
}

export const ModelLoadingStatus: React.FC<ModelLoadingStatusProps> = ({ 
  className = '', 
  showProxyStatus = true 
}) => {
  const { 
    modelsLoading, 
    modelsError, 
    availableModels, 
    currentProvider,
    proxyStatus,
    lastModelRefresh,
    loadModelsForProvider,
    checkProxyHealth
  } = useSettingsStore();

  const [proxyHealthy, setProxyHealthy] = useState<boolean | null>(null);
  const [isCheckingHealth, setIsCheckingHealth] = useState(false);

  // Periodic health check
  useEffect(() => {
    const checkHealth = async () => {
      if (proxyStatus?.isRunning) {
        const healthy = await checkProxyHealth();
        setProxyHealthy(healthy);
      }
    };

    checkHealth();
    const interval = setInterval(checkHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [checkProxyHealth, proxyStatus?.isRunning]);

  const handleRetry = async () => {
    if (currentProvider) {
      await loadModelsForProvider(currentProvider.id, true);
    }
  };

  const handleHealthCheck = async () => {
    setIsCheckingHealth(true);
    try {
      const healthy = await checkProxyHealth();
      setProxyHealthy(healthy);
    } finally {
      setIsCheckingHealth(false);
    }
  };

  const formatLastRefresh = (date: Date | null) => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  if (!currentProvider) return null;

  return (
    <div className={`model-loading-status ${className}`}>
      {/* Loading State */}
      {modelsLoading && (
        <div className="status-item loading">
          <div className="loading-spinner" />
          <span>Loading models for {currentProvider.name}...</span>
        </div>
      )}

      {/* Error State */}
      {modelsError && (
        <div className="status-item error">
          <span className="error-icon">⚠️</span>
          <div className="error-content">
            <span className="error-message">Error: {modelsError}</span>
            <button
              onClick={handleRetry}
              className="retry-button"
              disabled={modelsLoading}
            >
              {modelsLoading ? 'Retrying...' : 'Retry'}
            </button>
          </div>
        </div>
      )}

      {/* Success State */}
      {!modelsLoading && !modelsError && availableModels.length > 0 && (
        <div className="status-item success">
          <span className="success-icon">✅</span>
          <div className="success-content">
            <span>{availableModels.length} models available</span>
            {lastModelRefresh && (
              <span className="refresh-time">
                Last updated: {formatLastRefresh(lastModelRefresh)}
              </span>
            )}
          </div>
        </div>
      )}

      {/* No Models State */}
      {!modelsLoading && !modelsError && availableModels.length === 0 && (
        <div className="status-item warning">
          <span className="warning-icon">📭</span>
          <div className="warning-content">
            <span>No models found for {currentProvider.name}</span>
            <button
              onClick={handleRetry}
              className="retry-button"
              disabled={modelsLoading}
            >
              Refresh
            </button>
          </div>
        </div>
      )}

      {/* Proxy Status */}
      {showProxyStatus && proxyStatus && (
        <div className="proxy-status">
          <div className={`status-item ${proxyStatus.isRunning ? 'proxy-running' : 'proxy-stopped'}`}>
            <span className={`proxy-icon ${proxyStatus.isRunning ? '🟢' : '🔴'}`}>
              {proxyStatus.isRunning ? '🟢' : '🔴'}
            </span>
            <div className="proxy-content">
              <span>
                Proxy: {proxyStatus.isRunning ? 'Running' : 'Stopped'}
                {proxyHealthy === false && proxyStatus.isRunning && ' (Unhealthy)'}
              </span>
              {proxyStatus.isRunning && (
                <button
                  onClick={handleHealthCheck}
                  className="health-check-button"
                  disabled={isCheckingHealth}
                >
                  {isCheckingHealth ? 'Checking...' : 'Check Health'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      <style>{`
        .model-loading-status {
          padding: 8px;
          border-radius: 6px;
          background: var(--color-bg-secondary, #f5f5f5);
          border: 1px solid var(--color-border, #e0e0e0);
          font-size: 12px;
          color: var(--color-text-secondary, #666);
        }

        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
        }

        .status-item:last-child {
          margin-bottom: 0;
        }

        .loading-spinner {
          width: 12px;
          height: 12px;
          border: 2px solid var(--color-border, #e0e0e0);
          border-top: 2px solid var(--color-primary, #007acc);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .error-content,
        .success-content,
        .warning-content,
        .proxy-content {
          display: flex;
          flex-direction: column;
          gap: 4px;
          flex: 1;
        }

        .retry-button,
        .health-check-button {
          padding: 2px 8px;
          border: 1px solid var(--color-border, #e0e0e0);
          border-radius: 4px;
          background: var(--color-bg-primary, #fff);
          color: var(--color-text-primary, #333);
          cursor: pointer;
          font-size: 11px;
          align-self: flex-start;
        }

        .retry-button:hover,
        .health-check-button:hover {
          background: var(--color-bg-hover, #f0f0f0);
        }

        .retry-button:disabled,
        .health-check-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .refresh-time {
          font-size: 10px;
          opacity: 0.8;
        }

        .error {
          color: var(--color-error, #d32f2f);
        }

        .success {
          color: var(--color-success, #2e7d32);
        }

        .warning {
          color: var(--color-warning, #f57c00);
        }

        .proxy-status {
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid var(--color-border, #e0e0e0);
        }

        .proxy-running {
          color: var(--color-success, #2e7d32);
        }

        .proxy-stopped {
          color: var(--color-error, #d32f2f);
        }
      `}</style>
    </div>
  );
};

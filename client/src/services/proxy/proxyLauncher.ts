/**
 * ProxyLauncher - Manages the Node.js proxy server lifecycle within CEP environment
 * This service launches and manages the model proxy server that bypasses CORS limitations
 */

declare global {
  interface Window {
    CSInterface: any;
    cep: any;
  }
}

export class ProxyLauncher {
  private static instance: ProxyLauncher;
  private isLaunched = false;
  private proxyUrl = 'http://localhost:3131';
  private healthCheckInterval: NodeJS.Timeout | null = null;

  static getInstance(): ProxyLauncher {
    if (!ProxyLauncher.instance) {
      ProxyLauncher.instance = new ProxyLauncher();
    }
    return ProxyLauncher.instance;
  }

  /**
   * Launch the proxy server using CEP's Node.js capabilities
   */
  async launchProxy(): Promise<boolean> {
    if (this.isLaunched) {
      console.log('🔄 Proxy already launched, checking health...');
      const isHealthy = await this.checkProxyHealth();
      if (isHealthy) {
        return true;
      } else {
        console.log('🔄 Proxy unhealthy, attempting relaunch...');
        this.isLaunched = false;
      }
    }

    try {
      console.log('🚀 Launching SahAI Model Proxy Server...');

      // Check if we're in CEP environment
      if (typeof window !== 'undefined' && window.CSInterface) {
        return await this.launchViaCEP();
      } else {
        console.warn('⚠️ Not in CEP environment, proxy launch skipped');
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to launch proxy server:', error);
      return false;
    }
  }

  /**
   * Launch proxy via CEP's Node.js integration
   */
  private async launchViaCEP(): Promise<boolean> {
    try {
      const csInterface = new window.CSInterface();
      const extensionPath = csInterface.getSystemPath('extension');
      
      // Try to find Node.js executable
      const possibleNodePaths = [
        `${extensionPath}/node_modules/.bin/node`,
        `${extensionPath}/node_modules/node/bin/node`,
        'node' // System node
      ];

      const scriptPath = `${extensionPath}/scripts/model-proxy-server.js`;
      
      for (const nodePath of possibleNodePaths) {
        try {
          console.log(`🔄 Trying Node.js path: ${nodePath}`);
          
          const command = `"${nodePath}" "${scriptPath}"`;
          
          // Launch the process using CEP's process API
          const result = await new Promise<string>((resolve, reject) => {
            csInterface.evalScript(`
              try {
                var result = window.cep.process.createProcess("${command.replace(/"/g, '\\"')}");
                result;
              } catch (error) {
                "Error: " + error.toString();
              }
            `, (result: string) => {
              if (result === 'EvalScript error.' || result.startsWith('Error:')) {
                reject(new Error(result));
              } else {
                resolve(result);
              }
            });
          });

          console.log(`🔄 Process creation result: ${result}`);

          // Wait a moment for server to start
          await this.sleep(3000);
          
          // Test if proxy is responding
          const isHealthy = await this.checkProxyHealth();
          if (isHealthy) {
            this.isLaunched = true;
            this.startHealthMonitoring();
            console.log('✅ Proxy server launched successfully');
            return true;
          } else {
            console.warn(`⚠️ Proxy not responding after launch with ${nodePath}`);
          }
        } catch (error) {
          console.warn(`❌ Failed to launch with ${nodePath}:`, error);
          continue;
        }
      }

      throw new Error('Could not launch proxy with any Node.js path');
    } catch (error) {
      console.error('❌ CEP proxy launch failed:', error);
      return false;
    }
  }

  /**
   * Check if the proxy server is healthy
   */
  async checkProxyHealth(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${this.proxyUrl}/health`, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Proxy health check passed:`, data);
        return true;
      } else {
        console.warn(`⚠️ Proxy health check failed: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.warn(`⚠️ Proxy health check error:`, error);
      return false;
    }
  }

  /**
   * Start periodic health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.checkProxyHealth();
      if (!isHealthy && this.isLaunched) {
        console.warn('⚠️ Proxy became unhealthy, marking as not launched');
        this.isLaunched = false;
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Stop health monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Get the proxy server URL
   */
  getProxyUrl(): string {
    return this.proxyUrl;
  }

  /**
   * Check if proxy is currently running
   */
  isProxyRunning(): boolean {
    return this.isLaunched;
  }

  /**
   * Shutdown the proxy launcher (cleanup)
   */
  shutdown(): void {
    this.stopHealthMonitoring();
    this.isLaunched = false;
    console.log('🔄 Proxy launcher shutdown');
  }

  /**
   * Utility function to sleep for a given number of milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Test proxy connectivity with a simple model fetch
   */
  async testProxyConnectivity(providerId: string = 'openrouter'): Promise<boolean> {
    try {
      const response = await fetch(`${this.proxyUrl}/api/models/${providerId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ forceRefresh: false }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Proxy connectivity test passed for ${providerId}:`, data.models?.length || 0, 'models');
        return true;
      } else {
        console.warn(`⚠️ Proxy connectivity test failed for ${providerId}: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.warn(`⚠️ Proxy connectivity test error for ${providerId}:`, error);
      return false;
    }
  }
}

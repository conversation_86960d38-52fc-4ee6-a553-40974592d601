# 🚀 Successful Model Integration Implementation Plan
## Cline Logic → SahAI CEP Extension V2

Based on comprehensive analysis and proven CEP workaround strategies, this plan provides a **definitive roadmap** for successfully integrating Cline's model loading logic into the V2 architecture.

---

## 🎯 **Executive Summary**

**Goal**: Enable model loading without API keys (like <PERSON>line) while maintaining V2's superior UI/UX and provider coverage.

**Strategy**: Hybrid Node.js proxy approach that bypasses CORS limitations while preserving V2's architectural strengths.

**Timeline**: 2 weeks for core implementation + 1 week for polish and testing.

**Success Metrics**: 
- ✅ Models load without API keys for 5+ providers
- ✅ Background refresh works seamlessly  
- ✅ Error handling is production-ready
- ✅ Performance matches or exceeds current V2

---

## 🏗️ **Architecture Overview**

### Current V2 Architecture (Problematic)
```
React Panel → apiService.ts → External APIs (BLOCKED by CORS)
```

### New Hybrid Architecture (Solution)
```
React Panel → apiService.ts → Local Node.js Proxy → External APIs
                ↓
            CEP Storage (Caching & Persistence)
```

**Key Benefits**:
- ✅ **CORS Bypassed**: Node.js proxy handles all external requests
- ✅ **API Keys Secured**: Never exposed to client-side code
- ✅ **Rate Limiting**: Centralized control at proxy level
- ✅ **Caching**: Multi-layer caching strategy
- ✅ **Fallback**: Public → Authenticated → Cached → Default

---

## 📋 **Phase 1: Foundation Setup (Days 1-3)**

### 1.1 Enable Node.js in CEP Manifest

**File**: `CSXS/manifest.xml`

```xml
<DispatchInfo>
  <Resources>
    <MainPath>./client/index.html</MainPath>
    <ScriptPath>./extendscript/main.jsx</ScriptPath>
    <CEFCommandLine>
      <Parameter>--enable-nodejs</Parameter>
      <Parameter>--allow-file-access-from-files</Parameter>
      <Parameter>--mixed-context</Parameter>
      <Parameter>--disable-web-security</Parameter>
    </CEFCommandLine>
  </Resources>
</DispatchInfo>
```

### 1.2 Install Proxy Dependencies

```bash
npm install --save-dev express cors node-fetch@2 helmet compression
```

### 1.3 Create Smart Proxy Server

**File**: `scripts/model-proxy-server.js`

```javascript
const express = require('express');
const fetch = require('node-fetch');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

const app = express();
const PORT = 3131;

// Security and performance middleware
app.use(helmet());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(cors({ origin: '*' }));

// Provider configurations with public endpoints
const PROVIDER_CONFIGS = {
  'openrouter': {
    publicEndpoint: 'https://openrouter.ai/api/v1/models',
    authEndpoint: 'https://openrouter.ai/api/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'together': {
    publicEndpoint: 'https://api.together.xyz/models/info',
    authEndpoint: 'https://api.together.xyz/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'groq': {
    publicEndpoint: 'https://api.groq.com/openai/v1/models',
    authEndpoint: 'https://api.groq.com/openai/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  },
  'anthropic': {
    authEndpoint: 'https://api.anthropic.com/v1/models',
    authHeader: 'x-api-key',
    authPrefix: ''
  },
  'openai': {
    authEndpoint: 'https://api.openai.com/v1/models',
    authHeader: 'Authorization',
    authPrefix: 'Bearer '
  }
};

// In-memory cache with TTL
const modelCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Rate limiting
const rateLimits = new Map();
const RATE_LIMIT = 60; // requests per minute per provider

// Main endpoint for model fetching
app.post('/api/models/:providerId', async (req, res) => {
  const { providerId } = req.params;
  const { apiKey, forceRefresh = false } = req.body;

  try {
    // Check rate limits
    if (isRateLimited(providerId)) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded', 
        retryAfter: 60 
      });
    }

    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = getFromCache(providerId);
      if (cached) {
        console.log(`Cache hit for ${providerId}`);
        return res.json(cached);
      }
    }

    const config = PROVIDER_CONFIGS[providerId];
    if (!config) {
      return res.status(400).json({ 
        error: `Unsupported provider: ${providerId}` 
      });
    }

    let models = [];

    // Strategy 1: Try public endpoint first (like Cline)
    if (config.publicEndpoint) {
      try {
        console.log(`Attempting public fetch for ${providerId}`);
        const response = await fetch(config.publicEndpoint, {
          timeout: 10000,
          headers: {
            'User-Agent': 'SahAI-CEP-Extension/2.0'
          }
        });

        if (response.ok) {
          const data = await response.json();
          models = normalizeModels(providerId, data);
          console.log(`Public fetch successful for ${providerId}: ${models.length} models`);
        }
      } catch (error) {
        console.warn(`Public fetch failed for ${providerId}:`, error.message);
      }
    }

    // Strategy 2: Try authenticated endpoint if API key provided and public failed
    if (models.length === 0 && apiKey && config.authEndpoint) {
      try {
        console.log(`Attempting authenticated fetch for ${providerId}`);
        const headers = {
          'User-Agent': 'SahAI-CEP-Extension/2.0',
          'Content-Type': 'application/json'
        };
        headers[config.authHeader] = config.authPrefix + apiKey;

        const response = await fetch(config.authEndpoint, {
          timeout: 15000,
          headers
        });

        if (response.ok) {
          const data = await response.json();
          models = normalizeModels(providerId, data);
          console.log(`Authenticated fetch successful for ${providerId}: ${models.length} models`);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error(`Authenticated fetch failed for ${providerId}:`, error.message);
        return res.status(500).json({ 
          error: `Authentication failed: ${error.message}` 
        });
      }
    }

    // Strategy 3: Return default models if all else fails
    if (models.length === 0) {
      models = getDefaultModels(providerId);
      console.log(`Using default models for ${providerId}: ${models.length} models`);
    }

    // Cache the results
    setCache(providerId, models);
    updateRateLimit(providerId);

    res.json({
      provider: providerId,
      models,
      cached: false,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`Error fetching models for ${providerId}:`, error);
    res.status(500).json({ 
      error: error.message,
      provider: providerId 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    cache: {
      size: modelCache.size,
      providers: Array.from(modelCache.keys())
    }
  });
});

// Utility functions
function normalizeModels(providerId, data) {
  // Provider-specific normalization logic
  switch (providerId) {
    case 'openrouter':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.name || model.id,
        description: model.description,
        contextLength: model.context_length,
        inputCost: model.pricing?.prompt,
        outputCost: model.pricing?.completion,
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));
    
    case 'openai':
    case 'groq':
      return (data.data || []).map(model => ({
        id: model.id,
        name: model.id,
        description: `${model.id} model`,
        contextLength: getContextLength(model.id),
        provider: providerId,
        isAvailable: true,
        lastUpdated: new Date()
      }));
    
    default:
      return [];
  }
}

function getContextLength(modelId) {
  const contextMap = {
    'gpt-4': 8192,
    'gpt-4-turbo': 128000,
    'gpt-3.5-turbo': 4096,
    'claude-3-sonnet': 200000,
    'claude-3-haiku': 200000,
    'llama-3-70b': 8192
  };
  return contextMap[modelId] || 4096;
}

function getDefaultModels(providerId) {
  const defaults = {
    'openrouter': [
      { id: 'openai/gpt-4', name: 'GPT-4', provider: providerId, isAvailable: true, lastUpdated: new Date() },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude 3 Sonnet', provider: providerId, isAvailable: true, lastUpdated: new Date() }
    ],
    'openai': [
      { id: 'gpt-4', name: 'GPT-4', provider: providerId, isAvailable: true, lastUpdated: new Date() },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: providerId, isAvailable: true, lastUpdated: new Date() }
    ]
  };
  return defaults[providerId] || [];
}

// Cache management
function getFromCache(providerId) {
  const entry = modelCache.get(providerId);
  if (entry && Date.now() - entry.timestamp < CACHE_TTL) {
    return entry.data;
  }
  return null;
}

function setCache(providerId, data) {
  modelCache.set(providerId, {
    data: { provider: providerId, models: data, cached: true, timestamp: new Date().toISOString() },
    timestamp: Date.now()
  });
}

// Rate limiting
function isRateLimited(providerId) {
  const limit = rateLimits.get(providerId);
  if (!limit) return false;
  
  const now = Date.now();
  if (now - limit.resetTime > 60000) {
    rateLimits.delete(providerId);
    return false;
  }
  
  return limit.count >= RATE_LIMIT;
}

function updateRateLimit(providerId) {
  const now = Date.now();
  const limit = rateLimits.get(providerId) || { count: 0, resetTime: now };
  
  if (now - limit.resetTime > 60000) {
    limit.count = 1;
    limit.resetTime = now;
  } else {
    limit.count++;
  }
  
  rateLimits.set(providerId, limit);
}

app.listen(PORT, () => {
  console.log(`🚀 SahAI Model Proxy Server running on port ${PORT}`);
  console.log(`📊 Supported providers: ${Object.keys(PROVIDER_CONFIGS).join(', ')}`);
});
```

---

## 📋 **Phase 2: Client Integration (Days 4-7)**

### 2.1 Create Proxy Launcher

**File**: `client/src/services/proxy/proxyLauncher.ts`

```typescript
import { CSInterface } from 'csinterface-ts';

export class ProxyLauncher {
  private static instance: ProxyLauncher;
  private isLaunched = false;
  private proxyUrl = 'http://localhost:3131';

  static getInstance(): ProxyLauncher {
    if (!ProxyLauncher.instance) {
      ProxyLauncher.instance = new ProxyLauncher();
    }
    return ProxyLauncher.instance;
  }

  async launchProxy(): Promise<boolean> {
    if (this.isLaunched) return true;

    try {
      const csInterface = new CSInterface();
      const extensionPath = csInterface.getSystemPath('extension');
      
      // Try to find Node.js executable
      const possibleNodePaths = [
        `${extensionPath}/node_modules/.bin/node`,
        `${extensionPath}/node_modules/node/bin/node`,
        'node' // System node
      ];

      const scriptPath = `${extensionPath}/scripts/model-proxy-server.js`;
      
      for (const nodePath of possibleNodePaths) {
        try {
          const command = `"${nodePath}" "${scriptPath}"`;
          
          await new Promise((resolve, reject) => {
            csInterface.evalScript(`
              var result = window.cep.process.createProcess("${command}");
              result;
            `, (result) => {
              if (result === 'EvalScript error.') {
                reject(new Error('Failed to launch proxy'));
              } else {
                resolve(result);
              }
            });
          });

          // Wait a moment for server to start
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Test if proxy is responding
          const isHealthy = await this.checkProxyHealth();
          if (isHealthy) {
            this.isLaunched = true;
            console.log('✅ Proxy server launched successfully');
            return true;
          }
        } catch (error) {
          console.warn(`Failed to launch with ${nodePath}:`, error);
          continue;
        }
      }

      throw new Error('Could not launch proxy with any Node.js path');
    } catch (error) {
      console.error('❌ Failed to launch proxy server:', error);
      return false;
    }
  }

  async checkProxyHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.proxyUrl}/health`, {
        method: 'GET',
        timeout: 5000
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  getProxyUrl(): string {
    return this.proxyUrl;
  }

  isProxyRunning(): boolean {
    return this.isLaunched;
  }
}
```

### 2.2 Refactor API Service for Proxy Integration

**File**: `client/src/services/api/apiService.ts`

```typescript
import { ProxyLauncher } from '../proxy/proxyLauncher';
import { ModelInfo } from '../../stores/settingsStore';

export class ApiService {
  private proxyLauncher: ProxyLauncher;
  private cache = new Map<string, { data: ModelInfo[]; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.proxyLauncher = ProxyLauncher.getInstance();
  }

  /**
   * Initialize the proxy server (call this once on app startup)
   */
  async initialize(): Promise<boolean> {
    console.log('🔄 Initializing API service...');
    const success = await this.proxyLauncher.launchProxy();
    if (success) {
      console.log('✅ API service initialized successfully');
    } else {
      console.error('❌ Failed to initialize API service');
    }
    return success;
  }

  /**
   * Fetch models for a provider (with hybrid public/authenticated approach)
   */
  async fetchModels(providerId: string, apiKey?: string, forceRefresh = false): Promise<ModelInfo[]> {
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = this.getFromCache(providerId);
      if (cached) {
        console.log(`📦 Cache hit for ${providerId}: ${cached.length} models`);
        return cached;
      }
    }

    // Ensure proxy is running
    if (!this.proxyLauncher.isProxyRunning()) {
      const launched = await this.proxyLauncher.launchProxy();
      if (!launched) {
        throw new Error('Proxy server is not available');
      }
    }

    try {
      const proxyUrl = this.proxyLauncher.getProxyUrl();
      const response = await fetch(`${proxyUrl}/api/models/${providerId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ apiKey, forceRefresh }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models = data.models || [];

      // Cache the results
      this.setCache(providerId, models);

      console.log(`✅ Fetched ${models.length} models for ${providerId} ${data.cached ? '(cached)' : '(fresh)'}`);
      return models;

    } catch (error) {
      console.error(`❌ Failed to fetch models for ${providerId}:`, error);

      // Try to return cached data as fallback
      const cached = this.getFromCache(providerId, true); // Allow stale cache
      if (cached) {
        console.log(`📦 Using stale cache for ${providerId}: ${cached.length} models`);
        return cached;
      }

      // Final fallback to default models
      const defaultModels = this.getDefaultModels(providerId);
      console.log(`🔄 Using default models for ${providerId}: ${defaultModels.length} models`);
      return defaultModels;
    }
  }

  /**
   * Batch fetch models for multiple providers
   */
  async fetchMultipleProviders(providerConfigs: Array<{providerId: string, apiKey?: string}>): Promise<Record<string, ModelInfo[]>> {
    const results: Record<string, ModelInfo[]> = {};

    // Fetch in parallel with some delay to avoid overwhelming APIs
    const promises = providerConfigs.map(async (config, index) => {
      // Stagger requests by 500ms each
      await new Promise(resolve => setTimeout(resolve, index * 500));

      try {
        const models = await this.fetchModels(config.providerId, config.apiKey);
        results[config.providerId] = models;
      } catch (error) {
        console.error(`Failed to fetch models for ${config.providerId}:`, error);
        results[config.providerId] = [];
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  /**
   * Check if proxy server is healthy
   */
  async checkHealth(): Promise<boolean> {
    return this.proxyLauncher.checkProxyHealth();
  }

  // Cache management
  private getFromCache(providerId: string, allowStale = false): ModelInfo[] | null {
    const entry = this.cache.get(providerId);
    if (!entry) return null;

    const isStale = Date.now() - entry.timestamp > this.CACHE_TTL;
    if (isStale && !allowStale) return null;

    return entry.data;
  }

  private setCache(providerId: string, models: ModelInfo[]): void {
    this.cache.set(providerId, {
      data: models,
      timestamp: Date.now()
    });
  }

  private getDefaultModels(providerId: string): ModelInfo[] {
    const defaults: Record<string, ModelInfo[]> = {
      'openrouter': [
        {
          id: 'openai/gpt-4',
          name: 'GPT-4',
          description: 'Most capable GPT-4 model',
          contextLength: 8192,
          provider: 'openrouter',
          isAvailable: true,
          lastUpdated: new Date()
        },
        {
          id: 'anthropic/claude-3-sonnet',
          name: 'Claude 3 Sonnet',
          description: 'Balanced performance model',
          contextLength: 200000,
          provider: 'openrouter',
          isAvailable: true,
          lastUpdated: new Date()
        }
      ],
      'openai': [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          description: 'Most capable GPT-4 model',
          contextLength: 8192,
          provider: 'openai',
          isAvailable: true,
          lastUpdated: new Date()
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          description: 'Fast and efficient model',
          contextLength: 4096,
          provider: 'openai',
          isAvailable: true,
          lastUpdated: new Date()
        }
      ]
    };

    return defaults[providerId] || [];
  }
}

// Singleton instance
export const apiService = new ApiService();
```

### 2.3 Update Settings Store Integration

**File**: `client/src/stores/settingsStore.ts` (Update the loadModelsForProvider method)

```typescript
// Replace the existing loadModelsForProvider method with this enhanced version
loadModelsForProvider: async (providerId: string, forceRefresh = false) => {
  console.log(`🔄 Loading models for provider: ${providerId}`);

  const state = get();
  set({ modelsLoading: true, modelsError: null });

  try {
    const provider = state.providers.find(p => p.id === providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    // Use the new API service with proxy
    const models = await apiService.fetchModels(
      providerId,
      provider.apiKey,
      forceRefresh
    );

    set({
      availableModels: models,
      modelsLoading: false,
      modelsError: null
    });

    // Auto-select first model if none selected
    const { currentModel } = get();
    if (!currentModel && models.length > 0) {
      get().setCurrentModel(models[0].id);
    }

    console.log(`✅ Loaded ${models.length} models for ${providerId}`);

  } catch (error) {
    console.error(`❌ Error loading models for ${providerId}:`, error);
    set({
      modelsLoading: false,
      modelsError: error instanceof Error ? error.message : 'Failed to load models',
      availableModels: []
    });
  }
},

// Add new method for background refresh
refreshAllProviders: async () => {
  const state = get();
  const configuredProviders = state.providers.filter(p => p.isConfigured || p.id === 'openrouter');

  console.log(`🔄 Background refresh for ${configuredProviders.length} providers`);

  // Use batch fetching for efficiency
  const providerConfigs = configuredProviders.map(p => ({
    providerId: p.id,
    apiKey: p.apiKey
  }));

  try {
    const results = await apiService.fetchMultipleProviders(providerConfigs);

    // Update models for current provider if it was refreshed
    const currentProviderId = state.currentProvider?.id;
    if (currentProviderId && results[currentProviderId]) {
      set({
        availableModels: results[currentProviderId],
        modelsError: null
      });
      console.log(`✅ Background refresh updated ${currentProviderId} with ${results[currentProviderId].length} models`);
    }
  } catch (error) {
    console.error('❌ Background refresh failed:', error);
  }
},
```

---

## 📋 **Phase 3: App Integration & Background Refresh (Days 8-10)**

### 3.1 Initialize Proxy on App Startup

**File**: `client/src/App.tsx` (Add to the main App component)

```typescript
import { useEffect } from 'react';
import { apiService } from './services/api/apiService';
import { useSettingsStore } from './stores/settingsStore';

function App() {
  const { refreshAllProviders } = useSettingsStore();

  useEffect(() => {
    let refreshInterval: NodeJS.Timeout;

    const initializeApp = async () => {
      console.log('🚀 Initializing SahAI CEP Extension...');

      // Initialize API service (launches proxy)
      const apiInitialized = await apiService.initialize();

      if (apiInitialized) {
        // Initial model refresh
        await refreshAllProviders();

        // Set up background refresh every 5 minutes
        refreshInterval = setInterval(() => {
          // Only refresh when document is visible
          if (!document.hidden) {
            console.log('🔄 Background model refresh triggered');
            refreshAllProviders();
          }
        }, 5 * 60 * 1000);

        console.log('✅ SahAI CEP Extension initialized successfully');
      } else {
        console.error('❌ Failed to initialize API service - some features may not work');
      }
    };

    initializeApp();

    // Cleanup
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshAllProviders]);

  // Rest of your App component...
}
```

### 3.2 Enhanced Error Handling & User Feedback

**File**: `client/src/components/common/ModelLoadingStatus.tsx`

```typescript
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { apiService } from '../../services/api/apiService';

export const ModelLoadingStatus: React.FC = () => {
  const { modelsLoading, modelsError, availableModels, currentProvider } = useSettingsStore();
  const [proxyHealth, setProxyHealth] = React.useState<boolean | null>(null);

  React.useEffect(() => {
    const checkHealth = async () => {
      const healthy = await apiService.checkHealth();
      setProxyHealth(healthy);
    };

    checkHealth();
    const interval = setInterval(checkHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (!currentProvider) return null;

  return (
    <div className="model-loading-status">
      {modelsLoading && (
        <div className="status-item loading">
          <div className="loading-spinner" />
          <span>Loading models for {currentProvider.name}...</span>
        </div>
      )}

      {modelsError && (
        <div className="status-item error">
          <span className="error-icon">⚠️</span>
          <span>Error: {modelsError}</span>
          <button
            onClick={() => useSettingsStore.getState().loadModelsForProvider(currentProvider.id, true)}
            className="retry-button"
          >
            Retry
          </button>
        </div>
      )}

      {!modelsLoading && !modelsError && availableModels.length > 0 && (
        <div className="status-item success">
          <span className="success-icon">✅</span>
          <span>{availableModels.length} models available</span>
        </div>
      )}

      {proxyHealth === false && (
        <div className="status-item warning">
          <span className="warning-icon">🔧</span>
          <span>Proxy service unavailable - using cached models</span>
        </div>
      )}
    </div>
  );
};
```

---

## 🎯 **Success Validation & Testing**

### Testing Checklist

#### Phase 1 Validation (Days 1-3)
- [ ] **Proxy launches successfully** on extension startup
- [ ] **Health endpoint responds** at `http://localhost:3131/health`
- [ ] **OpenRouter public API** returns models without API key
- [ ] **Error handling works** when proxy is unavailable

#### Phase 2 Validation (Days 4-7)
- [ ] **Models load in UI** for OpenRouter without API key
- [ ] **Provider switching** updates model list correctly
- [ ] **API key authentication** works for providers that require it
- [ ] **Cache system** reduces redundant API calls

#### Phase 3 Validation (Days 8-10)
- [ ] **Background refresh** updates models automatically
- [ ] **Error recovery** handles network failures gracefully
- [ ] **Performance** is acceptable (< 2s model loading)
- [ ] **Memory usage** is stable over time

### Production Readiness Checklist
- [ ] **Security**: API keys never exposed to client-side
- [ ] **Rate limiting**: Respects provider API limits
- [ ] **Error handling**: Graceful degradation in all scenarios
- [ ] **Caching**: Efficient cache management with TTL
- [ ] **Logging**: Comprehensive logging for debugging
- [ ] **Performance**: Optimized for CEP environment

### Testing Commands

```bash
# Test proxy server directly
curl http://localhost:3131/health

# Test OpenRouter public endpoint
curl -X POST http://localhost:3131/api/models/openrouter \
  -H "Content-Type: application/json" \
  -d '{}'

# Test with API key
curl -X POST http://localhost:3131/api/models/openai \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "your-api-key"}'
```

---

## 🚨 **Troubleshooting Guide**

### Common Issues & Solutions

#### Issue 1: Proxy Server Won't Start
**Symptoms**: Extension loads but no models appear
**Diagnosis**: Check browser console for proxy launch errors
**Solutions**:
1. Verify Node.js is installed and accessible
2. Check CEP manifest has `--enable-nodejs` parameter
3. Ensure port 3131 is not in use by another process
4. Try alternative Node.js paths in `proxyLauncher.ts`

#### Issue 2: CORS Errors Despite Proxy
**Symptoms**: Network errors when fetching models
**Diagnosis**: Check if requests are going to proxy (localhost:3131)
**Solutions**:
1. Verify proxy server is running (`/health` endpoint)
2. Check apiService is using correct proxy URL
3. Ensure proxy has CORS headers configured

#### Issue 3: Models Not Loading for Specific Providers
**Symptoms**: Some providers work, others don't
**Diagnosis**: Check proxy server logs for API errors
**Solutions**:
1. Verify provider endpoints in `PROVIDER_CONFIGS`
2. Check API key format and authentication headers
3. Test provider APIs directly with curl
4. Implement provider-specific error handling

#### Issue 4: High Memory Usage
**Symptoms**: Extension becomes slow over time
**Diagnosis**: Monitor cache size and background refresh frequency
**Solutions**:
1. Implement cache size limits
2. Reduce background refresh frequency
3. Clear old cache entries periodically
4. Optimize model data structures

---

## 🎉 **Expected Outcomes**

After implementing this plan, the V2 extension will have:

✅ **Cline-like Model Loading**: Models appear without API keys for supported providers
✅ **Superior UI/UX**: Maintains V2's advanced model picker and filtering
✅ **Robust Architecture**: Proxy-based approach solves CORS and security issues
✅ **Production Ready**: Comprehensive error handling and caching
✅ **Scalable**: Easy to add new providers and endpoints
✅ **Performance Optimized**: Intelligent caching and background refresh
✅ **Security Focused**: API keys never exposed to client-side code

### Key Metrics
- **Model Loading Time**: < 2 seconds for cached, < 5 seconds for fresh
- **Background Refresh**: Every 5 minutes when app is active
- **Cache Hit Rate**: > 80% for frequently accessed providers
- **Error Recovery**: < 10 seconds to fallback to cached/default models
- **Memory Usage**: Stable over 8+ hours of usage

### Supported Providers (Day 1)
1. **OpenRouter** - Public API ✅
2. **Together AI** - Public API ✅
3. **Groq** - Public API ✅

### Supported Providers (Week 2)
4. **OpenAI** - Authenticated API ✅
5. **Anthropic** - Authenticated API ✅
6. **Google Gemini** - Authenticated API ✅

**Timeline**: 10 days for full implementation + 4 days for testing and polish = **2 weeks total**

This hybrid approach combines the best of Cline's functionality with V2's architectural strengths, resulting in a superior model integration system that's both powerful and maintainable.
